using api.coleta.Data.Repositories;
using api.coleta.Models.Entidades;
using Microsoft.EntityFrameworkCore;

namespace api.coleta.Repositories
{
    public class PontoColetadoRepository : GenericRepository<PontoColetado>
    {
        public PontoColetadoRepository(ApplicationDbContext context) : base(context) { }

        public async Task<List<PontoColetado>> BuscarPontosPorColetaAsync(Guid coletaId)
        {
            return await Context.PontoColetados
                .Where(p => p.ColetaID == coletaId)
                .OrderByDescending(p => p.DataColeta)
                .ToListAsync();
        }

        public async Task<PontoColetado?> BuscarUltimoPontoColetadoAsync(Guid coletaId)
        {
            return await Context.PontoColetados
                .Where(p => p.ColetaID == coletaId)
                .OrderByDescending(p => p.DataColeta)
                .FirstOrDefaultAsync();
        }

        public async Task<int> ContarPontosColetadosAsync(Guid coletaId)
        {
            return await Context.PontoColetados
                .Where(p => p.ColetaID == coletaId)
                .CountAsync();
        }

        public async Task<DateTime?> ObterDataPrimeiraColetaAsync(Guid coletaId)
        {
            var primeiraColeta = await Context.PontoColetados
                .Where(p => p.ColetaID == coletaId)
                .OrderBy(p => p.DataColeta)
                .FirstOrDefaultAsync();

            return primeiraColeta?.DataColeta;
        }

        public async Task<DateTime?> ObterDataUltimaColetaAsync(Guid coletaId)
        {
            var ultimaColeta = await Context.PontoColetados
                .Where(p => p.ColetaID == coletaId)
                .OrderByDescending(p => p.DataColeta)
                .FirstOrDefaultAsync();

            return ultimaColeta?.DataColeta;
        }

        public async Task<List<PontoColetado>> BuscarPontosPorFuncionarioAsync(Guid funcionarioId, DateTime? dataInicio = null, DateTime? dataFim = null)
        {
            var query = Context.PontoColetados.Where(p => p.FuncionarioID == funcionarioId);

            if (dataInicio.HasValue)
                query = query.Where(p => p.DataColeta >= dataInicio.Value);

            if (dataFim.HasValue)
                query = query.Where(p => p.DataColeta <= dataFim.Value);

            return await query
                .OrderByDescending(p => p.DataColeta)
                .ToListAsync();
        }

        public async Task<List<PontoColetado>> BuscarPontosPorPeriodoAsync(DateTime dataInicio, DateTime dataFim)
        {
            return await Context.PontoColetados
                .Where(p => p.DataColeta >= dataInicio && p.DataColeta <= dataFim)
                .OrderByDescending(p => p.DataColeta)
                .ToListAsync();
        }
    }
}
