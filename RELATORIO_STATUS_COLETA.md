# Relatório de Status da Coleta

## Visão Geral

O sistema de relatório de status da coleta foi implementado para fornecer informações detalhadas sobre o progresso das coletas de solo, incluindo estatísticas de pontos coletados, dados do último ponto coletado, informações da fazenda, cliente, talhão e responsável pela coleta.

## Endpoints Disponíveis

### 1. Obter Relatório de Status de uma Coleta Específica

**GET** `/api/relatorio-status-coleta/{coletaId}`

Retorna o relatório detalhado de status para uma coleta específica.

**Parâmetros:**
- `coletaId` (Guid): ID da coleta

**Resposta:**
```json
{
  "coletaId": "guid",
  "nomeColeta": "string",
  "fazenda": {
    "id": "guid",
    "nome": "string",
    "endereco": "string",
    "lat": 0.0,
    "lng": 0.0,
    "clienteID": "guid"
  },
  "cliente": {
    "id": "guid",
    "nome": "string",
    "cpf": "string",
    "email": "string",
    "telefone": "string"
  },
  "talhao": {
    "id": "guid",
    "nome": "string",
    "area": "string",
    "observacao": "string"
  },
  "responsavelColeta": {
    "id": "guid",
    "nomeCompleto": "string",
    "cpf": "string",
    "email": "string",
    "telefone": "string"
  },
  "estatisticas": {
    "totalPontosPlanejados": 0,
    "totalPontosColetados": 0,
    "pontosRestantes": 0,
    "percentualConcluido": 0.0,
    "dataInicioColeta": "datetime",
    "dataUltimaColeta": "datetime"
  },
  "ultimoPontoColetado": {
    "pontoId": 0,
    "hexagonId": 0,
    "latitude": 0.0,
    "longitude": 0.0,
    "dataColeta": "datetime",
    "funcionario": {
      "id": "guid",
      "nomeCompleto": "string",
      "cpf": "string",
      "email": "string",
      "telefone": "string"
    }
  },
  "tipoColeta": "string",
  "profundidade": "string",
  "tipoAnalise": ["string"],
  "observacao": "string",
  "dataCriacao": "datetime"
}
```

### 2. Listar Relatórios de Status com Filtros

**GET** `/api/relatorio-status-coleta`

Lista relatórios de status de coletas com filtros opcionais.

**Parâmetros de Query:**
- `coletaId` (Guid, opcional): Filtrar por ID da coleta
- `fazendaId` (Guid, opcional): Filtrar por ID da fazenda
- `clienteId` (Guid, opcional): Filtrar por ID do cliente
- `responsavelId` (Guid, opcional): Filtrar por ID do responsável
- `dataInicio` (DateTime, opcional): Filtrar por data de início
- `dataFim` (DateTime, opcional): Filtrar por data de fim
- `tipoColeta` (string, opcional): Filtrar por tipo de coleta
- `apenasEmAndamento` (bool, opcional): Mostrar apenas coletas em andamento

**Exemplo de uso:**
```
GET /api/relatorio-status-coleta?apenasEmAndamento=true&tipoColeta=Hexagonal
```

### 3. Obter Resumo Estatístico

**GET** `/api/relatorio-status-coleta/resumo`

Retorna um resumo estatístico de todas as coletas do usuário.

**Resposta:**
```json
{
  "totalColetas": 0,
  "coletasEmAndamento": 0,
  "coletasConcluidas": 0,
  "totalPontosPlanejados": 0,
  "totalPontosColetados": 0,
  "percentualGeralConcluido": 0.0,
  "ultimaAtividade": "datetime"
}
```

### 4. Obter Coletas em Andamento

**GET** `/api/relatorio-status-coleta/em-andamento`

Retorna apenas as coletas que ainda não foram concluídas (percentual < 100%).

## Autenticação

Todos os endpoints requerem autenticação via Bearer Token no header:
```
Authorization: Bearer {seu-token-jwt}
```

## Estrutura de Dados

### Estatísticas da Coleta
- **Total de Pontos Planejados**: Número total de pontos definidos no GeoJSON da coleta
- **Total de Pontos Coletados**: Número de pontos efetivamente coletados (registrados na tabela PontoColetado)
- **Pontos Restantes**: Diferença entre planejados e coletados
- **Percentual Concluído**: Percentual de conclusão da coleta
- **Data Início/Última Coleta**: Primeira e última data de coleta registrada

### Último Ponto Coletado
Informações do último ponto coletado, incluindo:
- Coordenadas (latitude/longitude)
- Data e hora da coleta
- Funcionário responsável pela coleta

## Casos de Uso

1. **Monitoramento de Progresso**: Acompanhar o andamento das coletas em tempo real
2. **Gestão de Equipes**: Identificar responsáveis e últimas atividades
3. **Planejamento**: Visualizar pontos restantes e priorizar coletas
4. **Relatórios Gerenciais**: Obter estatísticas consolidadas para tomada de decisão

## Exemplo de Integração

```javascript
// Obter relatório de uma coleta específica
const response = await fetch('/api/relatorio-status-coleta/12345678-1234-1234-1234-123456789012', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const relatorio = await response.json();
console.log(`Progresso: ${relatorio.estatisticas.percentualConcluido}%`);
console.log(`Pontos restantes: ${relatorio.estatisticas.pontosRestantes}`);
```

## Observações

- O sistema calcula automaticamente as estatísticas baseado nos dados do GeoJSON (pontos planejados) e da tabela PontoColetado (pontos efetivamente coletados)
- Os dados são filtrados por usuário, garantindo que cada usuário veja apenas suas próprias coletas
- O percentual de conclusão é calculado em tempo real baseado nos dados atuais
