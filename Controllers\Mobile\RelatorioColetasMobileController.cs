using api.cliente.Interfaces;
using api.coleta.Models.DTOs;
using api.coleta.Services;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace api.coleta.Controllers.Mobile
{
    [ApiController]
    [Route("api/mobile/relatorio-coletas")]
    public class RelatorioColetasMobileController : BaseController
    {
        private readonly IJwtToken _jwtToken;
        private readonly RelatorioColetasPendentesFinalizadasService _relatorioService;

        public RelatorioColetasMobileController(
            INotificador notificador,
            IJwtToken jwtToken,
            RelatorioColetasPendentesFinalizadasService relatorioService) : base(notificador)
        {
            _jwtToken = jwtToken;
            _relatorioService = relatorioService;
        }

        /// <summary>
        /// Gera relatório completo de coletas pendentes e finalizadas
        /// </summary>
        /// <returns>Relatório com coletas pendentes, finalizadas e resumo</returns>
        [HttpGet]
        [Route("completo")]
        [Authorize]
        public async Task<IActionResult> RelatorioCompleto()
        {
            var token = ObterIDDoToken();
            if (token == null)
            {
                return BadRequest(new { message = "Token inválido." });
            }

            Guid userId = (Guid)_jwtToken.ObterUsuarioIdDoToken(token);
            
            var relatorio = await _relatorioService.GerarRelatorioColetasPendentesFinalizadasAsync(userId);
            
            return Ok(relatorio);
        }

        /// <summary>
        /// Lista apenas coletas pendentes (não iniciadas ou em andamento)
        /// </summary>
        /// <returns>Lista de coletas pendentes com dados completos</returns>
        [HttpGet]
        [Route("pendentes")]
        [Authorize]
        public async Task<IActionResult> ColetasPendentes()
        {
            var token = ObterIDDoToken();
            if (token == null)
            {
                return BadRequest(new { message = "Token inválido." });
            }

            Guid userId = (Guid)_jwtToken.ObterUsuarioIdDoToken(token);
            
            var coletasPendentes = await _relatorioService.ListarColetasPendentesAsync(userId);
            
            return Ok(new
            {
                total = coletasPendentes.Count,
                coletas = coletasPendentes
            });
        }

        /// <summary>
        /// Lista apenas coletas finalizadas (com relatório gerado)
        /// </summary>
        /// <returns>Lista de coletas finalizadas com dados completos</returns>
        [HttpGet]
        [Route("finalizadas")]
        [Authorize]
        public async Task<IActionResult> ColetasFinalizadas()
        {
            var token = ObterIDDoToken();
            if (token == null)
            {
                return BadRequest(new { message = "Token inválido." });
            }

            Guid userId = (Guid)_jwtToken.ObterUsuarioIdDoToken(token);
            
            var coletasFinalizadas = await _relatorioService.ListarColetasFinalizadasAsync(userId);
            
            return Ok(new
            {
                total = coletasFinalizadas.Count,
                coletas = coletasFinalizadas
            });
        }

        /// <summary>
        /// Obtém resumo estatístico das coletas
        /// </summary>
        /// <returns>Resumo com totais e estatísticas gerais</returns>
        [HttpGet]
        [Route("resumo")]
        [Authorize]
        public async Task<IActionResult> ResumoColetas()
        {
            var token = ObterIDDoToken();
            if (token == null)
            {
                return BadRequest(new { message = "Token inválido." });
            }

            Guid userId = (Guid)_jwtToken.ObterUsuarioIdDoToken(token);
            
            var relatorio = await _relatorioService.GerarRelatorioColetasPendentesFinalizadasAsync(userId);
            
            return Ok(relatorio.Resumo);
        }

        /// <summary>
        /// Lista coletas agrupadas por fazenda (similar ao endpoint mobile original)
        /// </summary>
        /// <returns>Coletas organizadas por fazenda com status</returns>
        [HttpGet]
        [Route("por-fazenda")]
        [Authorize]
        public async Task<IActionResult> ColetasPorFazenda()
        {
            var token = ObterIDDoToken();
            if (token == null)
            {
                return BadRequest(new { message = "Token inválido." });
            }

            Guid userId = (Guid)_jwtToken.ObterUsuarioIdDoToken(token);
            
            var relatorio = await _relatorioService.GerarRelatorioColetasPendentesFinalizadasAsync(userId);
            var todasColetas = relatorio.ColetasPendentes.Concat(relatorio.ColetasFinalizadas).ToList();

            // Agrupar por fazenda
            var coletasPorFazenda = todasColetas
                .Where(c => c.Fazenda != null)
                .GroupBy(c => c.Fazenda.Id)
                .Select(g => new
                {
                    fazendaId = g.Key,
                    nomeFazenda = g.First().Fazenda.Nome,
                    fazenda = g.First().Fazenda,
                    cliente = g.First().Cliente,
                    totalColetas = g.Count(),
                    coletasPendentes = g.Count(c => c.Status != StatusColeta.Finalizada),
                    coletasFinalizadas = g.Count(c => c.Status == StatusColeta.Finalizada),
                    totalPontosPlanejados = g.Sum(c => c.TotalPontosPlanejados),
                    totalPontosColetados = g.Sum(c => c.TotalPontosColetados),
                    percentualConcluido = g.Count() > 0 ? Math.Round(g.Average(c => c.PercentualConcluido), 2) : 0,
                    ultimaAtividade = g.Where(c => c.DataUltimaColeta.HasValue)
                                      .OrderByDescending(c => c.DataUltimaColeta)
                                      .FirstOrDefault()?.DataUltimaColeta,
                    coletas = g.OrderByDescending(c => c.DataUltimaColeta ?? c.DataCriacao).ToList()
                })
                .OrderBy(f => f.nomeFazenda)
                .ToList();

            return Ok(coletasPorFazenda);
        }

        /// <summary>
        /// Lista coletas com filtros específicos
        /// </summary>
        /// <param name="status">Filtro por status: pendente, em-andamento, finalizada</param>
        /// <param name="fazendaId">Filtro por ID da fazenda</param>
        /// <param name="dataInicio">Data de início para filtro</param>
        /// <param name="dataFim">Data de fim para filtro</param>
        /// <returns>Lista filtrada de coletas</returns>
        [HttpGet]
        [Route("filtradas")]
        [Authorize]
        public async Task<IActionResult> ColetasFiltradas(
            [FromQuery] string? status = null,
            [FromQuery] Guid? fazendaId = null,
            [FromQuery] DateTime? dataInicio = null,
            [FromQuery] DateTime? dataFim = null)
        {
            var token = ObterIDDoToken();
            if (token == null)
            {
                return BadRequest(new { message = "Token inválido." });
            }

            Guid userId = (Guid)_jwtToken.ObterUsuarioIdDoToken(token);
            
            var relatorio = await _relatorioService.GerarRelatorioColetasPendentesFinalizadasAsync(userId);
            var todasColetas = relatorio.ColetasPendentes.Concat(relatorio.ColetasFinalizadas).AsQueryable();

            // Aplicar filtros
            if (!string.IsNullOrEmpty(status))
            {
                switch (status.ToLower())
                {
                    case "pendente":
                        todasColetas = todasColetas.Where(c => c.Status == StatusColeta.Pendente);
                        break;
                    case "em-andamento":
                        todasColetas = todasColetas.Where(c => c.Status == StatusColeta.EmAndamento);
                        break;
                    case "finalizada":
                        todasColetas = todasColetas.Where(c => c.Status == StatusColeta.Finalizada);
                        break;
                }
            }

            if (fazendaId.HasValue)
            {
                todasColetas = todasColetas.Where(c => c.Fazenda != null && c.Fazenda.Id == fazendaId.Value);
            }

            if (dataInicio.HasValue)
            {
                todasColetas = todasColetas.Where(c => c.DataCriacao >= dataInicio.Value);
            }

            if (dataFim.HasValue)
            {
                todasColetas = todasColetas.Where(c => c.DataCriacao <= dataFim.Value);
            }

            var resultado = todasColetas
                .OrderByDescending(c => c.DataUltimaColeta ?? c.DataCriacao)
                .ToList();

            return Ok(new
            {
                total = resultado.Count,
                filtros = new
                {
                    status,
                    fazendaId,
                    dataInicio,
                    dataFim
                },
                coletas = resultado
            });
        }
    }
}
