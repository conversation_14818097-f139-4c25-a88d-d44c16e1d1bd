using api.cliente.Models.DTOs;
using api.fazenda.models;
using api.funcionario.Models.DTOs;
using api.talhao.Models.DTOs;

namespace api.coleta.Models.DTOs
{
    public class RelatorioStatusColetaDTO
    {
        public Guid ColetaId { get; set; }
        public string NomeColeta { get; set; }
        public FazendaResponseDTO Fazenda { get; set; }
        public ClienteResponseDTO Cliente { get; set; }
        public Talhoes Ta<PERSON>hao { get; set; }
        public UsuarioResponseDTO ResponsavelColeta { get; set; }
        public EstatisticasColetaDTO Estatisticas { get; set; }
        public UltimoPontoColetadoDTO UltimoPontoColetado { get; set; }
        public string TipoColeta { get; set; }
        public string Profundidade { get; set; }
        public List<string> TipoAnalise { get; set; }
        public string Observacao { get; set; }
        public DateTime DataCriacao { get; set; }
    }

    public class EstatisticasColetaDTO
    {
        public int TotalPontosPlanejados { get; set; }
        public int TotalPontosColetados { get; set; }
        public int PontosRestantes { get; set; }
        public decimal PercentualConcluido { get; set; }
        public DateTime? DataInicioColeta { get; set; }
        public DateTime? DataUltimaColeta { get; set; }
    }

    public class UltimoPontoColetadoDTO
    {
        public int? PontoId { get; set; }
        public int? HexagonId { get; set; }
        public double? Latitude { get; set; }
        public double? Longitude { get; set; }
        public DateTime? DataColeta { get; set; }
        public UsuarioResponseDTO Funcionario { get; set; }
    }

    public class QueryRelatorioStatusColeta
    {
        public Guid? ColetaId { get; set; }
        public Guid? FazendaId { get; set; }
        public Guid? ClienteId { get; set; }
        public Guid? ResponsavelId { get; set; }
        public DateTime? DataInicio { get; set; }
        public DateTime? DataFim { get; set; }
        public string? TipoColeta { get; set; }
        public bool? ApenasEmAndamento { get; set; }
    }

    public class RelatorioColetasPendentesFinalizadasDTO
    {
        public List<ColetaPendenteFinalizada> ColetasPendentes { get; set; } = new();
        public List<ColetaPendenteFinalizada> ColetasFinalizadas { get; set; } = new();
        public ResumoColetasDTO Resumo { get; set; } = new();
    }

    public class ColetaPendenteFinalizada
    {
        public Guid ColetaId { get; set; }
        public string NomeColeta { get; set; }
        public FazendaResponseDTO Fazenda { get; set; }
        public ClienteResponseDTO Cliente { get; set; }
        public Talhoes Talhao { get; set; }
        public UsuarioResponseDTO ResponsavelColeta { get; set; }
        public UltimoPontoColetadoDTO? UltimoPontoColetado { get; set; }
        public int TotalPontosPlanejados { get; set; }
        public int TotalPontosColetados { get; set; }
        public int PontosRestantes { get; set; }
        public decimal PercentualConcluido { get; set; }
        public DateTime? DataUltimaColeta { get; set; }
        public DateTime DataCriacao { get; set; }
        public string TipoColeta { get; set; }
        public string Profundidade { get; set; }
        public List<string> TipoAnalise { get; set; }
        public string Observacao { get; set; }
        public StatusColeta Status { get; set; }
    }

    public class ResumoColetasDTO
    {
        public int TotalColetasPendentes { get; set; }
        public int TotalColetasFinalizadas { get; set; }
        public int TotalColetas { get; set; }
        public int TotalPontosPlanejados { get; set; }
        public int TotalPontosColetados { get; set; }
        public int TotalPontosRestantes { get; set; }
        public decimal PercentualGeralConcluido { get; set; }
        public DateTime? DataUltimaAtividade { get; set; }
    }

    public enum StatusColeta
    {
        Pendente,
        EmAndamento,
        Finalizada
    }
}
