# Relatório de Coletas Mobile - API

## Endpoint: GET /api/mobile/coleta/relatorio

### Descrição
Esta rota retorna um relatório completo das coletas do usuário, incluindo informações sobre pontos coletados, estatísticas de progresso e dados da última coleta realizada.

### Autenticação
- **Requerida**: Sim
- **Tipo**: <PERSON><PERSON> (JWT)

### Parâmetros
- **Nenhum parâmetro necessário** - A rota utiliza o ID do usuário extraído do token JWT

### Resposta de Sucesso

**Status Code**: `200 OK`

**Estrutura da Resposta**:
```json
[
  {
    "id": "guid-da-coleta",
    "nomeColeta": "Nome do Talhão/Coleta",
    "talhao": {
      "id": "guid-do-talhao",
      "nome": "Nome do Talhão",
      "area": "100.5",
      "observacao": "Observações do talhão"
    },
    "funcionarioResponsavel": {
      "nomeCompleto": "<PERSON>",
      "cpf": "12345678901",
      "email": "<EMAIL>",
      "telefone": "11999999999"
    },
    "estatisticas": {
      "totalPontosPlanejados": 50,
      "totalPontosColetados": 30,
      "pontosRestantes": 20,
      "percentualConcluido": 60.00,
      "statusColeta": "Em Andamento"
    },
    "ultimaColeta": {
      "dataColeta": "2024-01-15T14:30:00Z",
      "latitude": -23.5505,
      "longitude": -46.6333,
      "funcionario": "Maria Santos"
    },
    "dataUltimaColeta": "2024-01-15T14:30:00Z",
    "tipoColeta": "Hexagonal",
    "profundidade": "Profundidade0_20",
    "observacao": "Observações da coleta",
    "dataCriacao": "2024-01-10T08:00:00Z"
  }
]
```

### Campos da Resposta

#### Objeto Principal
- **id**: ID único da coleta
- **nomeColeta**: Nome da coleta (baseado no nome do talhão)
- **talhao**: Informações do talhão
- **funcionarioResponsavel**: Dados do funcionário responsável pela coleta
- **estatisticas**: Estatísticas de progresso da coleta
- **ultimaColeta**: Informações do último ponto coletado
- **dataUltimaColeta**: Data/hora da última coleta realizada
- **tipoColeta**: Tipo de coleta (Hexagonal, Retangular, PontosAmostrais)
- **profundidade**: Profundidade da coleta
- **observacao**: Observações da coleta
- **dataCriacao**: Data de criação da coleta

#### Estatísticas
- **totalPontosPlanejados**: Total de pontos planejados para coleta
- **totalPontosColetados**: Quantidade de pontos já coletados
- **pontosRestantes**: Quantidade de pontos restantes para coletar
- **percentualConcluido**: Percentual de conclusão da coleta (0-100)
- **statusColeta**: Status atual da coleta:
  - `"Pendente"`: Nenhum ponto coletado
  - `"Em Andamento"`: Alguns pontos coletados
  - `"Finalizada"`: Todos os pontos coletados

#### Última Coleta
- **dataColeta**: Data/hora do último ponto coletado
- **latitude**: Coordenada de latitude do último ponto
- **longitude**: Coordenada de longitude do último ponto
- **funcionario**: Nome do funcionário que coletou o último ponto

### Exemplo de Uso

```bash
curl -X GET "https://api.exemplo.com/api/mobile/coleta/relatorio" \
  -H "Authorization: Bearer seu_token_jwt_aqui" \
  -H "Content-Type: application/json"
```

### Resposta de Erro

**Token Inválido** - Status Code: `400 Bad Request`
```json
{
  "message": "Token inválido."
}
```

**Não Autorizado** - Status Code: `401 Unauthorized`
```json
{
  "message": "Acesso negado."
}
```

### Notas Importantes

1. **Dados em Tempo Real**: O relatório consulta a tabela `PontoColetado` para obter estatísticas atualizadas
2. **Filtro por Usuário**: Apenas coletas do usuário autenticado são retornadas
3. **Performance**: A consulta é otimizada para buscar apenas os dados necessários
4. **Dados Nulos**: Campos podem ser `null` se não houver dados disponíveis (ex: `ultimaColeta` se nenhum ponto foi coletado)

### Integração com Sistema Existente

Esta rota complementa as rotas existentes:
- **GET /api/mobile/coleta/listar**: Lista coletas básicas
- **GET /api/mobile/coleta/listar-por-fazenda**: Lista coletas agrupadas por fazenda
- **POST /api/mobile/coleta/salva**: Salva pontos coletados (que alimentam este relatório)

### Casos de Uso

1. **Dashboard Mobile**: Exibir progresso das coletas em tempo real
2. **Relatórios Gerenciais**: Acompanhar produtividade dos funcionários
3. **Planejamento**: Identificar coletas pendentes e em andamento
4. **Auditoria**: Rastrear última atividade de coleta por funcionário
