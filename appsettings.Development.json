{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "server=**************;port=3319;database=db;user=root;password=*********;"}, "GoogleApi": {"ApiKey": "AIzaSyBaTTZemsy0hmVZ1hVQwxD4XT6dhjf5ZLY"}, "Jwt": {"SecretKey": "x0o_+(2n#wjp$%z#xfbkovk(tf@5gc9=@pb1*gi+&t4)fldj#+", "Issuer": "coletaapi", "Audience": "coletaapi"}, "Minio": {"Endpoint": "apis-minio.uwqcav.easypanel.host", "AccessKey": "fLcdHPZhcymKr6XBKXLn", "SecretKey": "f5NVPdNW7ivSgv5C0gP3Ub7B1JkwDb2wtPsW5Peh"}}