using api.cliente.Models.DTOs;
using api.cliente.Services;
using api.coleta.Models.DTOs;
using api.coleta.Models.Entidades;
using api.coleta.Repositories;
using api.coleta.Services;
using api.fazenda.models;
using api.fazenda.repositories;
using api.talhao.Models.DTOs;
using api.talhao.Services;
using AutoMapper;
using System.Text.Json;

namespace api.coleta.Services
{
    public class RelatorioColetasPendentesFinalizadasService : ServiceBase
    {
        private readonly VisualizarMapaRepository _visualizarMapaRepository;
        private readonly PontoColetadoRepository _pontoColetadoRepository;
        private readonly GeoJsonRepository _geoJsonRepository;
        private readonly TalhaoService _talhaoService;
        private readonly UsuarioService _usuarioService;
        private readonly FazendaService _fazendaService;
        private readonly ClienteService _clienteService;
        private readonly RelatorioRepository _relatorioRepository;

        public RelatorioColetasPendentesFinalizadasService(
            VisualizarMapaRepository visualizarMapaRepository,
            PontoColetadoRepository pontoColetadoRepository,
            GeoJsonRepository geoJsonRepository,
            TalhaoService talhaoService,
            UsuarioService usuarioService,
            FazendaService fazendaService,
            ClienteService clienteService,
            RelatorioRepository relatorioRepository,
            IUnitOfWork unitOfWork,
            IMapper mapper) : base(unitOfWork, mapper)
        {
            _visualizarMapaRepository = visualizarMapaRepository;
            _pontoColetadoRepository = pontoColetadoRepository;
            _geoJsonRepository = geoJsonRepository;
            _talhaoService = talhaoService;
            _usuarioService = usuarioService;
            _fazendaService = fazendaService;
            _clienteService = clienteService;
            _relatorioRepository = relatorioRepository;
        }

        public async Task<RelatorioColetasPendentesFinalizadasDTO> GerarRelatorioColetasPendentesFinalizadasAsync(Guid userId)
        {
            // Buscar todas as coletas do usuário
            var todasColetas = _visualizarMapaRepository.ListarVisualizarMapaMobile(userId);

            var coletasPendentes = new List<ColetaPendenteFinalizada>();
            var coletasFinalizadas = new List<ColetaPendenteFinalizada>();

            foreach (var coleta in todasColetas)
            {
                var coletaProcessada = await ProcessarColetaAsync(coleta, userId);
                if (coletaProcessada != null)
                {
                    // Verificar se a coleta tem relatório (finalizada)
                    var temRelatorio = _relatorioRepository.BuscarRelatorioPorColetaId(coleta.Id) != null;

                    if (temRelatorio)
                    {
                        coletaProcessada.Status = StatusColeta.Finalizada;
                        coletasFinalizadas.Add(coletaProcessada);
                    }
                    else
                    {
                        // Determinar se está pendente ou em andamento
                        coletaProcessada.Status = coletaProcessada.PercentualConcluido > 0
                            ? StatusColeta.EmAndamento
                            : StatusColeta.Pendente;
                        coletasPendentes.Add(coletaProcessada);
                    }
                }
            }

            // Calcular resumo
            var resumo = CalcularResumo(coletasPendentes, coletasFinalizadas);

            return new RelatorioColetasPendentesFinalizadasDTO
            {
                ColetasPendentes = coletasPendentes.OrderByDescending(c => c.DataUltimaColeta ?? c.DataCriacao).ToList(),
                ColetasFinalizadas = coletasFinalizadas.OrderByDescending(c => c.DataUltimaColeta ?? c.DataCriacao).ToList(),
                Resumo = resumo
            };
        }

        public async Task<List<ColetaPendenteFinalizada>> ListarColetasPendentesAsync(Guid userId)
        {
            var relatorio = await GerarRelatorioColetasPendentesFinalizadasAsync(userId);
            return relatorio.ColetasPendentes;
        }

        public async Task<List<ColetaPendenteFinalizada>> ListarColetasFinalizadasAsync(Guid userId)
        {
            var relatorio = await GerarRelatorioColetasPendentesFinalizadasAsync(userId);
            return relatorio.ColetasFinalizadas;
        }

        private async Task<ColetaPendenteFinalizada?> ProcessarColetaAsync(Coleta coleta, Guid userId)
        {
            try
            {
                // Buscar dados relacionados
                var talhaoJson = _talhaoService.BuscarTalhaoJsonPorId(coleta.TalhaoID);
                if (talhaoJson == null) return null;

                var talhao = _talhaoService.BuscarTalhaoPorTalhaoJson(coleta.TalhaoID);
                if (talhao == null) return null;

                // Buscar fazenda e cliente usando os repositórios diretamente para evitar filtro de usuário
                var fazenda = _fazendaService.BuscarFazendaPorId(userId, talhao.FazendaID);
                var cliente = _clienteService.BuscarClientePorId(userId, talhao.ClienteID);

                // Se não encontrar com filtro de usuário, tentar buscar diretamente
                if (fazenda == null && talhao.Fazenda != null)
                {
                    fazenda = _mapper.Map<FazendaResponseDTO>(talhao.Fazenda);
                }

                if (cliente == null && talhao.Cliente != null)
                {
                    cliente = _mapper.Map<ClienteResponseDTO>(talhao.Cliente);
                }
                var responsavel = _usuarioService.BuscarUsuarioPorId(coleta.UsuarioRespID);

                // Buscar GeoJSON para contar pontos planejados
                var geojson = _geoJsonRepository.ObterPorId(coleta.GeojsonID);
                int totalPontosPlanejados = ContarPontosPlanejados(geojson);

                // Buscar estatísticas de pontos coletados
                var pontosColetados = await _pontoColetadoRepository.BuscarPontosPorColetaAsync(coleta.Id);
                var ultimoPontoColetado = await _pontoColetadoRepository.BuscarUltimoPontoColetadoAsync(coleta.Id);
                var dataUltimaColeta = await _pontoColetadoRepository.ObterDataUltimaColetaAsync(coleta.Id);

                // Calcular estatísticas
                int totalPontosColetados = pontosColetados.Count;
                int pontosRestantes = totalPontosPlanejados - totalPontosColetados;
                decimal percentualConcluido = totalPontosPlanejados > 0
                    ? Math.Round((decimal)totalPontosColetados / totalPontosPlanejados * 100, 2)
                    : 0;

                return new ColetaPendenteFinalizada
                {
                    ColetaId = coleta.Id,
                    NomeColeta = talhaoJson.Nome ?? "Coleta sem nome",
                    Fazenda = fazenda != null ? _mapper.Map<FazendaResponseDTO>(fazenda) : null,
                    Cliente = cliente != null ? _mapper.Map<ClienteResponseDTO>(cliente) : null,
                    Talhao = talhaoJson != null ? _mapper.Map<Talhoes>(talhaoJson) : null,
                    ResponsavelColeta = responsavel != null ? new UsuarioResponseDTO
                    {
                        Id = coleta.UsuarioRespID,
                        NomeCompleto = responsavel.NomeCompleto,
                        CPF = responsavel.CPF,
                        Email = responsavel.Email,
                        Telefone = responsavel.Telefone
                    } : null,
                    UltimoPontoColetado = await MontarUltimoPontoColetadoAsync(ultimoPontoColetado),
                    TotalPontosPlanejados = totalPontosPlanejados,
                    TotalPontosColetados = totalPontosColetados,
                    PontosRestantes = pontosRestantes,
                    PercentualConcluido = percentualConcluido,
                    DataUltimaColeta = dataUltimaColeta,
                    DataCriacao = coleta.DataInclusao,
                    TipoColeta = coleta.TipoColeta.ToString(),
                    Profundidade = coleta.Profundidade.ToString(),
                    TipoAnalise = coleta.TipoAnalise?.Select(t => t.ToString()).ToList() ?? new List<string>(),
                    Observacao = coleta.Observacao ?? ""
                };
            }
            catch
            {
                return null;
            }
        }

        private ResumoColetasDTO CalcularResumo(List<ColetaPendenteFinalizada> pendentes, List<ColetaPendenteFinalizada> finalizadas)
        {
            var todasColetas = pendentes.Concat(finalizadas).ToList();

            return new ResumoColetasDTO
            {
                TotalColetasPendentes = pendentes.Count,
                TotalColetasFinalizadas = finalizadas.Count,
                TotalColetas = todasColetas.Count,
                TotalPontosPlanejados = todasColetas.Sum(c => c.TotalPontosPlanejados),
                TotalPontosColetados = todasColetas.Sum(c => c.TotalPontosColetados),
                TotalPontosRestantes = todasColetas.Sum(c => c.PontosRestantes),
                PercentualGeralConcluido = todasColetas.Count > 0
                    ? Math.Round(todasColetas.Average(c => c.PercentualConcluido), 2)
                    : 0,
                DataUltimaAtividade = todasColetas
                    .Where(c => c.DataUltimaColeta.HasValue)
                    .OrderByDescending(c => c.DataUltimaColeta)
                    .FirstOrDefault()?.DataUltimaColeta
            };
        }

        private int ContarPontosPlanejados(Geojson? geojson)
        {
            if (geojson == null || string.IsNullOrEmpty(geojson.Pontos))
                return 0;

            try
            {
                var pontosJson = JsonSerializer.Deserialize<JsonElement>(geojson.Pontos);

                if (pontosJson.ValueKind == JsonValueKind.Array)
                {
                    return pontosJson.GetArrayLength();
                }

                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<UltimoPontoColetadoDTO?> MontarUltimoPontoColetadoAsync(PontoColetado? ultimoPonto)
        {
            if (ultimoPonto == null)
                return null;

            var funcionario = _usuarioService.BuscarUsuarioPorId(ultimoPonto.FuncionarioID);

            return new UltimoPontoColetadoDTO
            {
                PontoId = (int)ultimoPonto.PontoID.GetHashCode(),
                HexagonId = (int)ultimoPonto.HexagonID.GetHashCode(),
                Latitude = ultimoPonto.Latitude,
                Longitude = ultimoPonto.Longitude,
                DataColeta = ultimoPonto.DataColeta,
                Funcionario = funcionario != null ? new UsuarioResponseDTO
                {
                    Id = ultimoPonto.FuncionarioID,
                    NomeCompleto = funcionario.NomeCompleto,
                    CPF = funcionario.CPF,
                    Email = funcionario.Email,
                    Telefone = funcionario.Telefone
                } : null
            };
        }
    }
}
