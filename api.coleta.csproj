﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\NovaPasta\**" />
    <Content Remove="Services\NovaPasta\**" />
    <EmbeddedResource Remove="Services\NovaPasta\**" />
    <None Remove="Services\NovaPasta\**" />
  </ItemGroup>

  <ItemGroup>
	  <PackageReference Include="AutoMapper" Version="13.0.1" />
	  <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.1" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.2">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.2" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.2">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
	  <PackageReference Include="Minio" Version="6.0.4" />
	  <PackageReference Include="NetTopologySuite" Version="2.6.0" />
	  <PackageReference Include="NetTopologySuite.Features" Version="2.2.0" />
	  <PackageReference Include="NetTopologySuite.IO.GeoJSON" Version="4.0.0" />
	  <PackageReference Include="NetTopologySuite.IO.SqlServerBytes" Version="2.1.0" />
	  <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
	  <PackageReference Include="Pomelo.EntityFrameworkCore.MySql" Version="8.0.2" />
	  <PackageReference Include="ProjNet" Version="2.0.0" />
	  <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
	  <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.6.2" />
	  <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

</Project>
