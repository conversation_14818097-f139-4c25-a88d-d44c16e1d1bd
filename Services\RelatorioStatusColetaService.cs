using api.cliente.Models.DTOs;
using api.cliente.Services;
using api.coleta.Models.DTOs;
using api.coleta.Models.Entidades;
using api.coleta.Repositories;
using api.coleta.Services;
using api.fazenda.models;
using api.fazenda.repositories;
using api.talhao.Models.DTOs;
using api.talhao.Services;
using AutoMapper;
using System.Text.Json;

namespace api.coleta.Services
{
    public class RelatorioStatusColetaService : ServiceBase
    {
        private readonly VisualizarMapaRepository _visualizarMapaRepository;
        private readonly PontoColetadoRepository _pontoColetadoRepository;
        private readonly GeoJsonRepository _geoJsonRepository;
        private readonly TalhaoService _talhaoService;
        private readonly UsuarioService _usuarioService;
        private readonly FazendaService _fazendaService;
        private readonly ClienteService _clienteService;

        public RelatorioStatusColetaService(
            VisualizarMapaRepository visualizarMapaRepository,
            PontoColetadoRepository pontoColetadoRepository,
            GeoJsonRepository geoJsonRepository,
            TalhaoService talhaoService,
            UsuarioService usuarioService,
            FazendaService fazendaService,
            ClienteService clienteService,
            IUnitOfWork unitOfWork,
            IMapper mapper) : base(unitOfWork, mapper)
        {
            _visualizarMapaRepository = visualizarMapaRepository;
            _pontoColetadoRepository = pontoColetadoRepository;
            _geoJsonRepository = geoJsonRepository;
            _talhaoService = talhaoService;
            _usuarioService = usuarioService;
            _fazendaService = fazendaService;
            _clienteService = clienteService;
        }

        public async Task<RelatorioStatusColetaDTO?> GerarRelatorioStatusColetaAsync(Guid coletaId, Guid userId)
        {
            // Buscar a coleta
            var coleta = _visualizarMapaRepository.BuscarVisualizarMapaPorId(userId, coletaId);
            if (coleta == null)
                return null;

            // Buscar dados relacionados
            var talhaoJson = _talhaoService.BuscarTalhaoJsonPorId(coleta.TalhaoID);
            if (talhaoJson == null)
                return null;

            var talhao = _talhaoService.BuscarTalhaoPorTalhaoJson(coleta.TalhaoID);
            if (talhao == null)
                return null;

            var fazenda = _fazendaService.BuscarFazendaPorId(userId, talhao.FazendaID);
            var cliente = _clienteService.BuscarClientePorId(userId, talhao.ClienteID);
            var responsavel = _usuarioService.BuscarUsuarioPorId(coleta.UsuarioRespID);

            // Buscar GeoJSON para contar pontos planejados
            var geojson = _geoJsonRepository.ObterPorId(coleta.GeojsonID);
            int totalPontosPlanejados = ContarPontosPlanejados(geojson);

            // Buscar estatísticas de pontos coletados
            var pontosColetados = await _pontoColetadoRepository.BuscarPontosPorColetaAsync(coletaId);
            var ultimoPontoColetado = await _pontoColetadoRepository.BuscarUltimoPontoColetadoAsync(coletaId);
            var dataInicioColeta = await _pontoColetadoRepository.ObterDataPrimeiraColetaAsync(coletaId);
            var dataUltimaColeta = await _pontoColetadoRepository.ObterDataUltimaColetaAsync(coletaId);

            // Calcular estatísticas
            int totalPontosColetados = pontosColetados.Count;
            int pontosRestantes = totalPontosPlanejados - totalPontosColetados;
            decimal percentualConcluido = totalPontosPlanejados > 0 
                ? Math.Round((decimal)totalPontosColetados / totalPontosPlanejados * 100, 2) 
                : 0;

            // Montar o relatório
            var relatorio = new RelatorioStatusColetaDTO
            {
                ColetaId = coleta.Id,
                NomeColeta = talhaoJson.Nome ?? "Coleta sem nome",
                Fazenda = fazenda != null ? _mapper.Map<FazendaResponseDTO>(fazenda) : null,
                Cliente = cliente != null ? _mapper.Map<ClienteResponseDTO>(cliente) : null,
                Talhao = talhaoJson != null ? _mapper.Map<Talhoes>(talhaoJson) : null,
                ResponsavelColeta = responsavel != null ? new UsuarioResponseDTO
                {
                    Id = coleta.UsuarioRespID,
                    NomeCompleto = responsavel.NomeCompleto,
                    CPF = responsavel.CPF,
                    Email = responsavel.Email,
                    Telefone = responsavel.Telefone
                } : null,
                TipoColeta = coleta.TipoColeta.ToString(),
                Profundidade = coleta.Profundidade.ToString(),
                TipoAnalise = coleta.TipoAnalise?.Select(t => t.ToString()).ToList() ?? new List<string>(),
                Observacao = coleta.Observacao ?? "",
                DataCriacao = coleta.DataInclusao,
                Estatisticas = new EstatisticasColetaDTO
                {
                    TotalPontosPlanejados = totalPontosPlanejados,
                    TotalPontosColetados = totalPontosColetados,
                    PontosRestantes = pontosRestantes,
                    PercentualConcluido = percentualConcluido,
                    DataInicioColeta = dataInicioColeta,
                    DataUltimaColeta = dataUltimaColeta
                },
                UltimoPontoColetado = await MontarUltimoPontoColetadoAsync(ultimoPontoColetado)
            };

            return relatorio;
        }

        private int ContarPontosPlanejados(Geojson? geojson)
        {
            if (geojson == null || string.IsNullOrEmpty(geojson.Pontos))
                return 0;

            try
            {
                var pontosJson = JsonSerializer.Deserialize<JsonElement>(geojson.Pontos);
                
                if (pontosJson.ValueKind == JsonValueKind.Array)
                {
                    return pontosJson.GetArrayLength();
                }
                
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        private async Task<UltimoPontoColetadoDTO?> MontarUltimoPontoColetadoAsync(PontoColetado? ultimoPonto)
        {
            if (ultimoPonto == null)
                return null;

            var funcionario = _usuarioService.BuscarUsuarioPorId(ultimoPonto.FuncionarioID);

            return new UltimoPontoColetadoDTO
            {
                PontoId = (int)ultimoPonto.PontoID.GetHashCode(),
                HexagonId = (int)ultimoPonto.HexagonID.GetHashCode(),
                Latitude = ultimoPonto.Latitude,
                Longitude = ultimoPonto.Longitude,
                DataColeta = ultimoPonto.DataColeta,
                Funcionario = funcionario != null ? new UsuarioResponseDTO
                {
                    Id = ultimoPonto.FuncionarioID,
                    NomeCompleto = funcionario.NomeCompleto,
                    CPF = funcionario.CPF,
                    Email = funcionario.Email,
                    Telefone = funcionario.Telefone
                } : null
            };
        }
    }
}
