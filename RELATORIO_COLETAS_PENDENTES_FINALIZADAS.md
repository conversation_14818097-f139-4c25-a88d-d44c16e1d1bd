# Relatório de Coletas Pendentes e Finalizadas - Mobile

## Visão Geral

Este sistema de relatório foi desenvolvido seguindo o padrão do controller mobile existente, fornecendo informações detalhadas sobre coletas pendentes e finalizadas, incluindo último ponto coletado, dados da fazenda, cliente, quantidade de pontos e progresso da coleta.

## Endpoints Disponíveis

### 1. Relatório Completo

**GET** `/api/mobile/relatorio-coletas/completo`

Retorna relatório completo com coletas pendentes, finalizadas e resumo estatístico.

**Resposta:**
```json
{
  "coletasPendentes": [
    {
      "coletaId": "guid",
      "nomeColeta": "string",
      "fazenda": {
        "id": "guid",
        "nome": "string",
        "endereco": "string",
        "lat": 0.0,
        "lng": 0.0
      },
      "cliente": {
        "id": "guid",
        "nome": "string",
        "cpf": "string",
        "email": "string"
      },
      "talhao": {
        "id": "guid",
        "nome": "string",
        "area": "string"
      },
      "responsavelColeta": {
        "id": "guid",
        "nomeCompleto": "string",
        "cpf": "string",
        "email": "string"
      },
      "ultimoPontoColetado": {
        "pontoId": 0,
        "latitude": 0.0,
        "longitude": 0.0,
        "dataColeta": "datetime",
        "funcionario": {
          "id": "guid",
          "nomeCompleto": "string"
        }
      },
      "totalPontosPlanejados": 0,
      "totalPontosColetados": 0,
      "pontosRestantes": 0,
      "percentualConcluido": 0.0,
      "dataUltimaColeta": "datetime",
      "dataCriacao": "datetime",
      "tipoColeta": "string",
      "profundidade": "string",
      "tipoAnalise": ["string"],
      "observacao": "string",
      "status": "Pendente|EmAndamento|Finalizada"
    }
  ],
  "coletasFinalizadas": [...],
  "resumo": {
    "totalColetasPendentes": 0,
    "totalColetasFinalizadas": 0,
    "totalColetas": 0,
    "totalPontosPlanejados": 0,
    "totalPontosColetados": 0,
    "totalPontosRestantes": 0,
    "percentualGeralConcluido": 0.0,
    "dataUltimaAtividade": "datetime"
  }
}
```

### 2. Coletas Pendentes

**GET** `/api/mobile/relatorio-coletas/pendentes`

Lista apenas coletas pendentes (não iniciadas ou em andamento).

**Resposta:**
```json
{
  "total": 0,
  "coletas": [...]
}
```

### 3. Coletas Finalizadas

**GET** `/api/mobile/relatorio-coletas/finalizadas`

Lista apenas coletas finalizadas (com relatório gerado).

**Resposta:**
```json
{
  "total": 0,
  "coletas": [...]
}
```

### 4. Resumo Estatístico

**GET** `/api/mobile/relatorio-coletas/resumo`

Obtém resumo estatístico das coletas.

### 5. Coletas por Fazenda

**GET** `/api/mobile/relatorio-coletas/por-fazenda`

Lista coletas agrupadas por fazenda (similar ao endpoint mobile original).

**Resposta:**
```json
[
  {
    "fazendaId": "guid",
    "nomeFazenda": "string",
    "fazenda": {...},
    "cliente": {...},
    "totalColetas": 0,
    "coletasPendentes": 0,
    "coletasFinalizadas": 0,
    "totalPontosPlanejados": 0,
    "totalPontosColetados": 0,
    "percentualConcluido": 0.0,
    "ultimaAtividade": "datetime",
    "coletas": [...]
  }
]
```

### 6. Coletas Filtradas

**GET** `/api/mobile/relatorio-coletas/filtradas`

Lista coletas com filtros específicos.

**Parâmetros de Query:**
- `status` (string, opcional): "pendente", "em-andamento", "finalizada"
- `fazendaId` (Guid, opcional): Filtrar por ID da fazenda
- `dataInicio` (DateTime, opcional): Data de início para filtro
- `dataFim` (DateTime, opcional): Data de fim para filtro

**Exemplo:**
```
GET /api/mobile/relatorio-coletas/filtradas?status=pendente&fazendaId=12345678-1234-1234-1234-123456789012
```

## Status das Coletas

- **Pendente**: Coleta criada mas sem pontos coletados
- **Em Andamento**: Coleta com alguns pontos coletados (0% < progresso < 100%)
- **Finalizada**: Coleta com relatório gerado no sistema

## Dados Incluídos

### Informações da Coleta
- Nome da coleta (baseado no talhão)
- Tipo de coleta e profundidade
- Tipos de análise
- Observações
- Data de criação

### Dados Contextuais
- **Fazenda**: Nome, endereço, coordenadas
- **Cliente**: Nome, CPF, email, telefone
- **Talhão**: Nome, área, observações
- **Responsável**: Funcionário responsável pela coleta

### Estatísticas de Progresso
- Total de pontos planejados (do GeoJSON)
- Total de pontos coletados (da tabela PontoColetado)
- Pontos restantes para coletar
- Percentual de conclusão
- Data da última coleta

### Último Ponto Coletado
- Coordenadas (latitude/longitude)
- Data e hora da coleta
- Funcionário que coletou

## Autenticação

Todos os endpoints requerem autenticação via Bearer Token:
```
Authorization: Bearer {seu-token-jwt}
```

## Casos de Uso

1. **Dashboard Mobile**: Visualizar status geral das coletas
2. **Gestão de Campo**: Identificar coletas pendentes e prioridades
3. **Relatórios Gerenciais**: Acompanhar progresso por fazenda
4. **Controle de Qualidade**: Verificar coletas finalizadas
5. **Planejamento**: Organizar trabalho de campo baseado em pendências

## Diferenças do Controller Mobile Original

- **Dados Enriquecidos**: Inclui estatísticas de progresso e último ponto
- **Status Inteligente**: Determina automaticamente se coleta está pendente, em andamento ou finalizada
- **Agrupamentos**: Organiza dados por fazenda com totalizadores
- **Filtros Avançados**: Permite filtrar por status, fazenda e período
- **Resumos**: Fornece estatísticas consolidadas

## Exemplo de Integração

```javascript
// Obter relatório completo
const response = await fetch('/api/mobile/relatorio-coletas/completo', {
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  }
});

const relatorio = await response.json();

console.log(`Coletas pendentes: ${relatorio.resumo.totalColetasPendentes}`);
console.log(`Coletas finalizadas: ${relatorio.resumo.totalColetasFinalizadas}`);
console.log(`Progresso geral: ${relatorio.resumo.percentualGeralConcluido}%`);

// Listar apenas pendentes
const pendentes = await fetch('/api/mobile/relatorio-coletas/pendentes', {
  headers: { 'Authorization': 'Bearer ' + token }
});

const coletasPendentes = await pendentes.json();
console.log(`${coletasPendentes.total} coletas precisam de atenção`);
```

## Observações

- Os dados são filtrados por usuário (responsável pela coleta)
- Coletas finalizadas são identificadas pela existência de relatório
- O sistema calcula automaticamente estatísticas em tempo real
- Compatível com a estrutura de dados do controller mobile existente
